<!DOCTYPE html>
<html lang="en">
  <head>
    <!-- Global site tag (gtag.js) - Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=UA-112307082-1"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'UA-112307082-1');
    </script>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <link rel="shortcut icon" href="img/favicon.ico"/>
    <title>Huaiwei Sun | Philosophia</title>
    <!-- Bootstrap core CSS -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css" integrity="sha384-BVYiiSIFeK1dGmJRAkycuHAHRg32OmUcww7on3RYdg4Va+PmSTsz/K68vbdEjh4u" crossorigin="anonymous">
    <!-- Loaded font -->
    <link href="https://fonts.googleapis.com/css?family=Cutive+Mono" rel="stylesheet">
    <!-- Main CSS -->
    <link rel="stylesheet" href="css/styles.css">
    <!-- Navigation Responsive CSS -->
    <link rel="stylesheet" href="css/responsive.css">



  </head>
  <body class="">

        <!-- Page Content -->
        <div class="container-fluid">
              <!-- Navigation -->
              <div id="header">
                <div class="row">
                  <div class="header-content clearfix col-lg-12">
                    <a class="logo" href="#"><img src="img/logo.png" alt="Huaiwei Sun"></a>
                    <nav id='navigation' class="navigation" role="navigation">
                      <ul class="primary-nav">
                        <li><a href="#" id="theme-toggle" onclick="toggleTheme()" style="cursor: pointer;">
                          <svg class="theme-icon moon" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path fill-rule="evenodd" d="M9.528 1.718a.75.75 0 01.162.819A8.97 8.97 0 009 6a9 9 0 009 9 8.97 8.97 0 003.463-.69.75.75 0 01.981.98 10.503 10.503 0 01-9.694 6.46c-5.799 0-10.5-4.701-10.5-10.5 0-4.368 2.667-8.112 6.46-9.694a.75.75 0 01.818.162z" clip-rule="evenodd" />
                          </svg>
                        </a></li>
  <!--                       <li><a href="profile.html">Profile</a></li> 
                        <li><a href="resume.html">Resume</a></li>
                        <li><a href="contact.html">Contact</a></li>-->
                      </ul>
                    </nav>
                  </div>
                </div>
              </div>
              <!-- Intro -->
              <div class="row">
                <div class="introduction">
                  <div class="col-12">
                    <p class="intro_1">
                      👋
                    </p>
                    <p class="intro_2">Huaiwei is simplifying <a href="https://www.anyscale.com" target="_blank" style = "color: var(--link-color); text-decoration: underline;">ML infra.</a></p>
                  </div>
                </div>
              </div>

              <!-- Info Banner -->
              <div class="row">
                <div class="col-12 banner-container">
                  <div class="info-banner">
                    Interested in my work? <a href="https://www.linkedin.com/in/scottsun94/" target="_blank">Connect on LinkedIn</a> for portfolio examples.
                  </div>
                </div>
              </div>

              <div class="row introduction">
                  <div class="col-12">
                    <p class="intro_3">Complexity happens. Simplicity is designed.</p>
                  </div>
              </div>

              <div class="row">
                <div class="col-12 footer" >
                    <span class="signature-1">
                      <a href="https://www.linkedin.com/in/scottsun94/" target="_blank" style = "color: var(--signature-color);">© 2025 Huaiwei Sun</a>
                    </span>
                </div>
              </div>



        </div>
      </div>


          
          


    <!-- Bootstrap core JavaScript -->
    <script src="https://code.jquery.com/jquery-3.3.1.slim.min.js" integrity="sha384-q8i/X+965DzO0rT7abK41JStQIAqVgRVzpbzo5smXKp4YfRvH+8abtTE1Pi6jizo" crossorigin="anonymous"></script>
    <!-- Bootstrap required JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.6/umd/popper.min.js" integrity="sha384-wHAiFfRlMFy6i5SRaxvfOCifBUQy1xHdJ/yoi7FRNXMRBu5WHdZYu1hA6ZOblgut" crossorigin="anonymous"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js" integrity="sha384-Tc5IQib027qvyjSMfHjOMaLkfuWVxZxUPnCJA7l2mCWNIpG9mGCD8wGNIcPD7Txa" crossorigin="anonymous"></script>
    <!-- Main JavaScript -->
    <script src="js/main.js"></script>
    <!-- Disable Hover on mobile -->
    <script type="text/javascript" src="js/hover_fix_mobile.js"></script>


    <!-- Dark/Light Mode Toggle Script -->
    <script>
      // Theme management
      function getPreferredTheme() {
        const storedTheme = localStorage.getItem('theme');
        if (storedTheme) {
          return storedTheme;
        }
        return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
      }

      function setTheme(theme) {
        // Update icon immediately before theme change to avoid color flash
        updateThemeToggleText(theme);
        document.documentElement.setAttribute('data-theme', theme);
        localStorage.setItem('theme', theme);
      }

      function updateThemeToggleText(themeOverride = null) {
        const currentTheme = themeOverride || document.documentElement.getAttribute('data-theme') || getPreferredTheme();
        const toggleButton = document.getElementById('theme-toggle');
        if (toggleButton) {
          const svg = toggleButton.querySelector('svg');
          if (svg) {
            if (currentTheme === 'dark') {
              // Sun icon for dark mode (click to go light) - orange color
              svg.setAttribute('class', 'theme-icon sun');
              svg.innerHTML = '<path fill-rule="evenodd" d="M12 2.25a.75.75 0 01.75.75v2.25a.75.75 0 01-1.5 0V3a.75.75 0 01.75-.75zM7.5 12a4.5 4.5 0 119 0 4.5 4.5 0 01-9 0zM18.894 6.166a.75.75 0 00-1.06-1.06l-1.591 1.59a.75.75 0 101.06 1.061l1.591-1.59zM21.75 12a.75.75 0 01-.75.75h-2.25a.75.75 0 010-1.5H21a.75.75 0 01.75.75zM17.834 18.894a.75.75 0 001.06-1.06l-1.59-1.591a.75.75 0 10-1.061 1.06l1.59 1.591zM12 18a.75.75 0 01.75.75V21a.75.75 0 01-1.5 0v-2.25A.75.75 0 0112 18zM7.758 17.303a.75.75 0 00-1.061-1.06l-1.591 1.59a.75.75 0 001.06 1.061l1.591-1.59zM6 12a.75.75 0 01-.75.75H3a.75.75 0 010-1.5h2.25A.75.75 0 016 12zM6.697 7.757a.75.75 0 001.06-1.06l-1.59-1.591a.75.75 0 00-1.061 1.06l1.59 1.591z" clip-rule="evenodd" />';
            } else {
              // Moon icon for light mode (click to go dark) - gray color
              svg.setAttribute('class', 'theme-icon moon');
              svg.innerHTML = '<path fill-rule="evenodd" d="M9.528 1.718a.75.75 0 01.162.819A8.97 8.97 0 009 6a9 9 0 009 9 8.97 8.97 0 003.463-.69.75.75 0 01.981.98 10.503 10.503 0 01-9.694 6.46c-5.799 0-10.5-4.701-10.5-10.5 0-4.368 2.667-8.112 6.46-9.694a.75.75 0 01.818.162z" clip-rule="evenodd" />';
            }
          }
        }
      }

      function toggleTheme() {
        const currentTheme = document.documentElement.getAttribute('data-theme') || getPreferredTheme();
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        setTheme(newTheme);
      }

      // Initialize theme on page load
      document.addEventListener('DOMContentLoaded', function() {
        setTheme(getPreferredTheme());

        // Listen for system theme changes
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', function(e) {
          if (!localStorage.getItem('theme')) {
            setTheme(e.matches ? 'dark' : 'light');
          }
        });
      });
    </script>
  </body>
</html>
