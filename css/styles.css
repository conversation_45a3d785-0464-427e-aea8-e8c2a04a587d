/* --------- CSS Variables for Dark/Light Mode ----------------------------------------*/

:root {
  /* Light mode colors */
  --bg-color: rgb(250,250,250);
  --text-color: #6c7279;
  --text-primary: #000000;
  --text-secondary: #424242;
  --text-tertiary: #515151;
  --link-color: #000000;
  --signature-color: rgb(150,150,150);
}

/* Dark mode colors */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-color: #1a1a1a;
    --text-color: #e0e0e0;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --text-tertiary: #b0b0b0;
    --link-color: #ffffff;
    --signature-color: #888888;
  }
}

/* Manual dark mode override */
[data-theme="dark"] {
  --bg-color: #1a1a1a;
  --text-color: #e0e0e0;
  --text-primary: #ffffff;
  --text-secondary: #cccccc;
  --text-tertiary: #b0b0b0;
  --link-color: #ffffff;
  --signature-color: #888888;
}

/* Manual light mode override */
[data-theme="light"] {
  --bg-color: rgb(250,250,250);
  --text-color: #6c7279;
  --text-primary: #000000;
  --text-secondary: #424242;
  --text-tertiary: #515151;
  --link-color: #000000;
  --signature-color: rgb(150,150,150);
}

/* --------- Base Styles ----------------------------------------*/

* {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
}

html {
	height: 100%;
}

body {
	font-family: outfit, -apple-system, BlinkMacSystemFont, 'Helvetica', Arial, sans-serif !important;
	font-weight: 400;
	color: var(--text-color);
	background-color: var(--bg-color);
	min-height: 100vh;
	margin: 0;
	transition: background-color 0.3s ease, color 0.3s ease;
}

a:hover, a:focus{
	text-decoration: none;
}

ul, ol {
	margin: 0;
	padding: 0;
}

.navigation ul {
	margin-bottom: 0 !important;
	display: flex;
	align-items: center;
	height: 100%;
}

.navigation li {
	display: flex;
	align-items: center;
}

.navigation a {
	display: flex;
	align-items: center;
	justify-content: center;
}

ul li {
	list-style: none;
}

/* --------- Header and Navigation ----------------------------------------*/

#header .header-content {
	position: relative;
	margin: 0 auto;
  	padding: 24px 0;
	height: 88px;
	-moz-transition: padding 0.3s;
	-o-transition: padding 0.3s;
	-webkit-transition: padding 0.3s;
	transition: padding 0.3s;
}

#header .logo {
  position: absolute;
  left: 5vw;
  top: 50%;
  transform: translateY(-50%);
}

.logo img {
	width: 60px;
	height: auto;
}

.navigation {
	position: absolute;
	right: 5vw;
	top: 50%;
	transform: translateY(-50%);
}

.navigation ul {
	margin: 0;
	padding: 0;
}

.navigation li {
	display: inline-block;
	margin-left: 30px;
}

.navigation a {
	color: var(--text-color);
	font-size: 16px;
	font-weight: 400;
	text-decoration: none;
	transition: color 0.3s ease;
}

.navigation a:hover {
	color: var(--text-primary);
}

/* Mobile menu button removed - using theme toggle for all screen sizes */

/* --------- Theme Toggle Icons ----------------------------------------*/

.theme-icon {
	transition: none !important;
}

.theme-icon.moon {
	color: #6b7280; /* Gray color for moon */
}

.theme-icon.sun {
	color: #f59e0b; /* Orange color for sun */
}

/* --------- Content Sections ----------------------------------------*/

.container-fluid {
	min-height: 100vh;
	display: flex;
	flex-direction: column;
}

.introduction {
	padding: 0 5vw;
	margin-bottom: 2rem;
	flex: 1;
}

.intro_1{
  	font-weight: 300;
	font-size: clamp(48px, 5vw, 5vw);
	color: var(--text-primary);
	margin-top: 12vh;
	letter-spacing: -1px;
	text-align: center;
}

.intro_2{
	font-weight: 700;
	font-size: 7rem;
	color: var(--text-primary);
	letter-spacing: -1px;
	line-height: 1.2;
	margin-top: 40px;
	text-align: center;
}

.intro_3{
	font-size: clamp(16px, 1.2vw, 16px);
	color: var(--text-tertiary);
	font-weight: 300;
	margin-top: 8vh;
	text-align: center;
	background: linear-gradient(to right, rgb(0, 0, 0) 0%, rgba(0, 0, 0, 0.8) 25%,rgb(248, 248, 248) 50%, rgba(0, 0, 0, 0.8) 75%,rgb(0, 0, 0) 100%);
	background-size: 200% 100%;
	background-clip: text;
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	animation: shimmer 2.5s linear infinite;
}

@keyframes shimmer {
	0% {
		background-position: 100% 0;
	}
	100% {
		background-position: -100% 0;
	}
}

/* --------- Info Banner ----------------------------------------*/

.info-banner {
	background-color: #f8f9fa; /* Light neutral background */
	border: 1px solid #e5e7eb; /* Light gray border */
	border-radius: 8px;
	padding: 12px 20px;
	margin: 4vh auto;
	display: inline-block;
	text-align: center;
	color: #374151; /* Dark gray text */
	font-size: 14px;
	font-weight: 500;
	position: relative;
}

.banner-container {
	text-align: center;
}

.info-banner a {
	color: #374151;
	text-decoration: underline;
	font-weight: 600;
}

.info-banner a:hover {
	color: #1f2937;
}

/* Dark mode styles for info banner */
@media (prefers-color-scheme: dark) {
	.info-banner {
		background-color: #1f2937; /* Dark neutral background */
		border-color: #4b5563; /* Medium gray border */
		color: #d1d5db; /* Light gray text */
	}

	.info-banner a {
		color: #d1d5db;
	}

	.info-banner a:hover {
		color: #f9fafb;
	}
}

[data-theme="dark"] .info-banner {
	background-color: #1f2937;
	border-color: #4b5563;
	color: #d1d5db;
}

[data-theme="dark"] .info-banner a {
	color: #d1d5db;
}

[data-theme="dark"] .info-banner a:hover {
	color: #f9fafb;
}

[data-theme="light"] .info-banner {
	background-color: #f8f9fa;
	border-color: #e5e7eb;
	color: #374151;
}

[data-theme="light"] .info-banner a {
	color: #374151;
}

[data-theme="light"] .info-banner a:hover {
	color: #1f2937;
}

/* --------- Footer ----------------------------------------*/

.footer {
	margin-top: auto;
	padding: 2rem 0;
	text-align: center;
	flex-shrink: 0;
}

.signature-1 {
	font-size: 14px;
	color: var(--signature-color);
}

.signature-1 a {
	color: var(--signature-color);
	text-decoration: none;
}

.signature-1 a:hover {
	text-decoration: underline;
}
